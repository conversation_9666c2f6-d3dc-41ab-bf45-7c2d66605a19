// Simple test script to verify login functionality
const fetch = require('node-fetch');

async function testLogin() {
  const baseUrl = 'http://localhost:3001';
  
  console.log('Testing login functionality...\n');
  
  // Test 1: Valid credentials (you'll need to register a user first)
  console.log('Test 1: Testing with valid credentials');
  try {
    const response = await fetch(`${baseUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'validpassword123'
      }),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Valid credentials test passed');
      console.log('Response:', data);
    } else {
      console.log('❌ Valid credentials test failed');
      console.log('Error:', data);
    }
  } catch (error) {
    console.log('❌ Valid credentials test failed with error:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: Invalid password
  console.log('Test 2: Testing with invalid password');
  try {
    const response = await fetch(`${baseUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      }),
    });
    
    const data = await response.json();
    
    if (!response.ok && data.message.includes('Invalid email or password')) {
      console.log('✅ Invalid password test passed');
      console.log('Response:', data);
    } else {
      console.log('❌ Invalid password test failed - should have been rejected');
      console.log('Response:', data);
    }
  } catch (error) {
    console.log('❌ Invalid password test failed with error:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 3: Non-existent email
  console.log('Test 3: Testing with non-existent email');
  try {
    const response = await fetch(`${baseUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'anypassword'
      }),
    });
    
    const data = await response.json();
    
    if (!response.ok && data.message.includes('Invalid email or password')) {
      console.log('✅ Non-existent email test passed');
      console.log('Response:', data);
    } else {
      console.log('❌ Non-existent email test failed - should have been rejected');
      console.log('Response:', data);
    }
  } catch (error) {
    console.log('❌ Non-existent email test failed with error:', error.message);
  }
}

// Run the test
testLogin().catch(console.error);
