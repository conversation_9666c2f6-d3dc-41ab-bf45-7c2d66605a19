import { FirebaseService } from "../../config/firebase/firebase.service";
import { UsersService } from "../users/users.service";
import type { RegisterDto } from "./dto/register.dto";
import type { LoginDto } from "./dto/login.dto";
export declare class AuthService {
    private firebaseService;
    private usersService;
    constructor(firebaseService: FirebaseService, usersService: UsersService);
    register(registerDto: RegisterDto): Promise<{
        message: string;
        userId: string;
    }>;
    validateUser(uid: string): Promise<{
        id: string;
        uid: string;
        email?: string;
        emailVerified: boolean;
        displayName?: string;
        photoURL?: string;
        phoneNumber?: string;
        disabled: boolean;
        metadata: import("firebase-admin/auth").UserMetadata;
        providerData: import("firebase-admin/auth").UserInfo[];
        passwordHash?: string;
        passwordSalt?: string;
        customClaims?: {
            [key: string]: any;
        };
        tenantId?: string | null;
        tokensValidAfterTime?: string;
        multiFactor?: import("firebase-admin/auth").MultiFactorSettings;
    }>;
    login(loginDto: LoginDto): Promise<{
        message: string;
        customToken: string;
        instructions: string;
        user: {
            userId: string;
            email: any;
            username: any;
            role: any;
        };
    }>;
    private verifyEmailPassword;
    updateLastLogin(userId: string): Promise<void>;
}
